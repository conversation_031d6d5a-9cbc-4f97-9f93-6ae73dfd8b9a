export function pieGetPercentage(value: number, length: number) {
  let percentage = "0";
  if (value > 0) {
    percentage = Math.floor((value / length) * 100) + "%";
  } else {
    percentage = "0%";
  }
  return percentage;
}

export function maskPhone(phone: string) {
  if (!phone) return;
  return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
}

export const roundNumber2 = (list: number[]) => {
  return list.map((item) => {
    return Math.round(item * 100);
  });
};

export const isJson = (str: string) => {
  try {
    JSON.parse(str);
    return true;
  } catch (error) {
    return false;
  }
};
