<template>
  <div class="teach-content">
    <!-- <div class="result-tab">
      <div
        v-for="tab in roleStuTabList"
        :class="`tab-item ${active === tab.questionType ? 'active' : ''}`"
        @click="onChangeTab(tab.questionType)"
      >
        <ysIcon class="icon" :type="tab.icon" />
        {{ tab.label }}
      </div>
    </div> -->
    <div class="result-content">
      <div class="title">
        <h3>{{ contentTitle }}</h3>
        <div v-if="active === 3" class="icons">
          <a-tooltip placement="bottom">
            <template #title>
              <span>内容概述</span>
            </template>
            <span class="icon-box" @click="onChangeTab(1)">
              <ys-icon class="icon" type="iconwenjian3" />
            </span>
          </a-tooltip>
          <a-tooltip placement="bottom">
            <template #title>
              <span>AI出题</span>
            </template>
            <span class="icon-box" @click="onChangeTab(4)">
              <ys-icon class="icon" type="icondengpao" />
            </span>
          </a-tooltip>
        </div>
        <ys-icon v-else type="iconshanchu2" style="font-size: 20px;color: #8C8C8C;cursor: pointer;" @click="onChangeTab(3)" />
      </div>

      <svg
        v-show="active === 3"
        id="markmapTeachContent"
        class="markmap-content"
      ></svg>
      <div
        v-show="active != 3"
        style="padding: 24px"
        class="AICONTENT"
        id="AICONTENT"
        v-html="html"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { marked } from "marked";
import { Transformer } from "markmap-lib";
import { Markmap } from "markmap-view";
import { ysIcon } from "@ys/ui";
import { isJson } from "@/utils";
import { ref, inject, Ref, onMounted, nextTick, computed } from "vue";
import * as d3 from 'd3';

const roleChats = inject<Ref<any>>("roleChats");
const active = ref(3);
const html = ref("");
const transformer = new Transformer();
let markmap: any;
const initOverview = async () => {
  const list = roleChats?.value[1];
  const item = list.find((item: any) => item.questionType === active.value);
  if (!item) return;
  const html1 = await marked.parse(item!.answer, {
    breaks: true,
  });
  html.value = html1;
};

const initMarkmap = async () => {
  markmap = Markmap.create("#markmapTeachContent");
  const list = roleChats?.value[1];
  const mindValue = list.find((item: any) => item.questionType === 2);
  if (!mindValue) return;

  if (isJson(mindValue.answer)) {
    // 将章节数据转换为思维导图格式
    const mindmapData = convertToMindmapData(JSON.parse(mindValue.answer));
    markmap.setData(mindmapData);
    addNodeClickEvents();
  } else {
    const { root } = await transformer.transform(mindValue.answer);
    markmap.setData(root);
  }

  markmap.fit();
};

/**
 * 为 markmap 节点添加点击事件
 */
const addNodeClickEvents = () => {
  nextTick(() => {
    const svg = d3.select('#markmapTeachContent');

    // 为所有节点添加点击事件
    svg.selectAll('g.markmap-node')
      .style('cursor', 'pointer')
      .on('click', function(event: any, d: any) {
        if (event.target.nodeName === 'DIV') {
          onNodeClick(d);
        }
      });
  });
};

/**
 * 将章节数据转换为思维导图格式
 * @param {Array} chapters - 章节数据数组
 * @returns {Object} 思维导图根节点数据
 */
const convertToMindmapData = (chapters: any[]) => {
  // 创建根节点
  const root: any = {
    type: 'root',
    content: '课程章节',
    children: [],
    payload: {}
  };

  // 为每个章节创建节点
  chapters.forEach((chapter) => {
    const chapterNode: any = {
      type: 'heading',
      content: chapter['章节名字'],
      children: [],
      payload: {
        time: chapter['时间']
      }
    };

    // 为每个章节要点创建子节点
    chapter['章节要点'].forEach((point: string) => {
      const pointNode = {
        type: 'list_item',
        content: point,
        children: [],
        payload: {
          time: chapter['时间'],
          parentTime: chapter['时间']
        }
      };
      chapterNode.children.push(pointNode);
    });

    root.children.push(chapterNode);
  });

  return root;
};

const timeStringToSeconds = (timeString: string): number => {
  const parts = timeString.split(':');

  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);
  const seconds = parseInt(parts[2], 10);

  return (hours * 3600) + (minutes * 60) + seconds;
};

const jumpVideoCurrentTime = (time: number) => {
  const aiVideo = document.getElementById("ai-video") as HTMLVideoElement;
  const aiVideo1 = document.getElementById("ai-video1") as HTMLVideoElement;
  if (aiVideo) {
    aiVideo.currentTime = time;
  }
  if (aiVideo1) {
    aiVideo1.currentTime = time;
  }
};

/**
 * 节点点击处理函数
 * @param {Object} nodeData - 节点数据对象
 */
const onNodeClick = (nodeData: any) => {
  const timeInfo = nodeData.data.payload?.time;
  if (timeInfo) {
    const time = timeStringToSeconds(timeInfo.split("-")[0]);
    jumpVideoCurrentTime(time);
  }
};

const onChangeTab = (questionType: number) => {
  active.value = questionType;
  if (questionType === 3) {
    if (markmap) return;
    initMarkmap();
  } else {
    initOverview();
  }
};

const contentTitle = computed(() => {
  if (active.value === 1) {
    return "内容概述";
  } else if (active.value === 4) {
    return "AI出题";
  } else {
    return "思维导图";
  }
});

onMounted(() => {
  initMarkmap();
});
</script>

<style lang="scss" scoped>
.teach-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .result-tab {
    display: flex;
    align-items: center;

    .tab-item + .tab-item {
      margin-left: 4px;
    }

    .tab-item {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      flex: 1;
      cursor: pointer;
      font-weight: bold;
      color: #262626;
      position: relative;
      top: 1px;
      background: #fafafa;
      padding: 9px 16px;
      border-radius: 2px;
      border: 1px solid #e5e5e5;
      .icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }

    .tab-item.active {
      background: #fff;
      color: #007aff;
      border-bottom-color: #fff;
    }
  }
  .result-content {
    overflow-y: auto;
    flex: 1;
    border: 1px solid #e5e5e5;
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 20px;
      .icons {
        display: flex;
        .icon-box {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 24px;
          height: 24px;
          background-color: #F5F5F5;
          border-radius: 4px;
          cursor: pointer;
          &:first-of-type {
            margin-right: 20px;
          }
          &:hover {
            background-color: #E6F6FF;
            color: #007AFF;
          }
        }
      }
    }
  }
  .markmap-content {
    width: 100%;
    height: 575px;
  }
}
</style>
