<template>
  <div class="teach-content">
    <div class="result-tab">
      <div
        v-for="(tab, index) in roleTeaTabList"
        :class="`tab-item ${active === tab.questionType ? 'active' : ''}`"
        @click="onChangeTab(tab.questionType)"
      >
        <ysIcon class="icon" :type="tab.icon" />
        {{ tab.label }}
      </div>
    </div>
    <div class="result-content">
      <div
        style="padding: 24px"
        class="AICONTENT"
        id="AICONTENT"
        v-html="html"
      ></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import icon1 from "./../../assets/icon1.svg";
import icon2 from "./../../assets/icon2.svg";
import { roleTeaTabList } from "./../../entity";
import { marked } from "marked";
import { ysIcon } from "@ys/ui";
import { ref, inject, Ref, onMounted } from "vue";
import { isJson } from "@/utils";

const roleChats = inject<Ref<any>>("roleChats");
const active = ref(17);
const html = ref("");
const initOverview = async () => {
  const list = roleChats?.value[2];
  const item = list.find((item: any) => item.questionType === active.value);
  if (!item || !item.answer) return;
  if (active.value != 17) {
    // 导入
    const moreAnswer = list.find(
      (item: any) => item.questionType === active.value + 6
    );

    const temptHEAD = `<div class="tempt">
        <img src='${icon1}'/>
        <div class="tempt-h">技能应用情况</div>
      </div>`;
    let html1 = await marked.parse(item.answer, {
      breaks: true,
    });
    let html2 = "无";
    if (moreAnswer.answer) {
      html2 = await marked.parse(moreAnswer!.answer, { breaks: true });
    }

    const temptFooter = `<div class="tempt">
        <img src='${icon2}' />
        <div class="tempt-f">评价&建议</div>
      </div>`;
    html.value =
      temptHEAD +
      html1 +
      "<div style='margin-top: 40px'></div>" +
      temptFooter +
      html2;
    return;
  } else {
    if (isJson(item.answer)) {
      const answer = JSON.parse(item.answer);

      console.log('answer', answer);
      
    }
    const html1 = await marked.parse(item!.answer, {
      breaks: true,
    });
    html.value = html1;
  }
};

const onChangeTab = (questionType: number) => {
  active.value = questionType;
  initOverview();
};

onMounted(() => {
  initOverview();
});
</script>

<style lang="scss" scoped>
.teach-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .result-tab {
    display: flex;
    align-items: center;

    .tab-item + .tab-item {
      margin-left: 4px;
    }

    .tab-item {
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      flex: 1;
      cursor: pointer;
      font-weight: bold;
      color: #262626;
      position: relative;
      top: 1px;
      background: #fafafa;
      padding: 9px 16px;
      border-radius: 2px;
      border: 1px solid #e5e5e5;
      .icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }

    .tab-item.active {
      background: #fff;
      color: #007aff;
      border-bottom-color: #fff;
    }
  }
  .result-content {
    overflow-y: auto;
    flex: 1;
    border: 1px solid #e5e5e5;
  }
}
</style>
