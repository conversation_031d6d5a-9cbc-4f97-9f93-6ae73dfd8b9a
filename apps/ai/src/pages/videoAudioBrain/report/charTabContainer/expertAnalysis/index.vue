<template>
  <div class="expert-analysis-container">
    <div class="chart" id="expertAnalysis"></div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import * as echarts from "echarts";

const initEcharts = () => {
  const chartDom = document.getElementById("expertAnalysis")!;
  const myChart = echarts.init(chartDom);

  let option: any;
  option = {
    radar: {
      indicator: [
        { name: '导入', max: 10 },
        { name: '讲解', max: 10 },
        { name: '提问', max: 10 },
        { name: '变化', max: 10 },
        { name: '强化', max: 10 },
        { name: '结束', max: 10 },
      ],
      axisName: {
        formatter: function (indicatorName: string) {
          switch (indicatorName) {
            case '导入':
              return '{a|' + indicatorName + '}';
            case '讲解':
              return '{b|' + indicatorName + '}';
            case '提问':
              return '{c|' + indicatorName + '}';
            case '变化':
              return '{d|' + indicatorName + '}';
            case '强化':
              return '{e|' + indicatorName + '}';
            default:
              return '{f|' + indicatorName + '}';
          }
        },
        rich: {
          a: {
            color: '#fff',
            fontSize: 14,
            padding: [5, 7],
            borderRadius: 4,
            backgroundColor: '#13C2C2',
          },
          b: {
            color: '#fff',
            fontSize: 14,
            padding: [5, 7],
            borderRadius: 4,
            backgroundColor: '#E96D5C',
          },
          c: {
            color: '#fff',
            fontSize: 14,
            padding: [5, 7],
            borderRadius: 4,
            backgroundColor: '#17BE6B',
          },
          d: {
            color: '#fff',
            fontSize: 14,
            padding: [5, 7],
            borderRadius: 4,
            backgroundColor: '#7262FD',
          },
          e: {
            color: '#fff',
            fontSize: 14,
            padding: [5, 7],
            borderRadius: 4,
            backgroundColor: '#27A3FA',
          },
          f: {
            color: '#fff',
            fontSize: 14,
            padding: [5, 7],
            borderRadius: 4,
            backgroundColor: '#FAAD14',
          },
        }
      },
      splitNumber: 3,
      splitArea: {
        areaStyle: {
          color: ['#C4DFFF', '#D4ECFF', 'rgba(0, 122, 255, 0.08)'],
        }
      },
      splitLine: {
        lineStyle: {
          color: '#D4ECFF'
        }
      },
      axisLine: {
        lineStyle: {
          color: '#D4ECFF'
        }
      }
    },
    series: [
      {
        type: 'radar',
        symbol: 'emptyCircle',
        data: [
          {
            value: [4, 5, 6, 7, 2, 8],
            name: '数据',
            areaStyle: {
              color: 'rgba(0, 122, 255, 0.5)'
            },
            lineStyle: {
              color: '#007AFF'
            },
            itemStyle: {
              color: '#007AFF'
            }
          }
        ]
      }
    ]
  };

  option && myChart.setOption(option);


}

onMounted(() => {
  initEcharts();
});
</script>

<style lang="scss" scoped>
.expert-analysis-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.chart {
  width: 100%;
  height: 400px;
}
</style>