<template>
  <div class="teach-advice">
    <div class="tabs">
      <div class="tab-item" :class="{ active: activeTab === 1 }" @click="handleTabClick(1)">
        <ys-icon class="icon" type="iconyingyongzhongxin" />
        <span>总体建议</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 2 }" @click="handleTabClick(2)">
        <ys-icon class="icon" type="iconxing1" />
        <span>新课标&核心素养</span>
      </div>
      <div class="tab-item" :class="{ active: activeTab === 3 }" @click="handleTabClick(3)">
        <ys-icon class="icon" type="iconbanji2" />
        <span>教学设计建议</span>
      </div>
    </div>

    <div class="content">
      <div v-if="activeTab === 1" class="card-box">
        <card v-for="(item, index) in adviceData18" :key="index" :title="item['标题']" :content="item['详情']" />
      </div>
      <div v-if="activeTab === 2" class="card-box">
        <card v-for="(item, index) in adviceData32" :key="index" :title="item['标题']" :content="item['详情']" />
      </div>
      <div v-if="activeTab === 3" class="large-card-box">
        <largeCard v-for="(item, index) in adviceData24" :key="index" :title="item['标题']" :plan="item['方案']" :reason="item['理由']" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, Ref, onMounted } from 'vue';
import { ysIcon } from '@ys/ui';
import card from './card.vue';
import largeCard from './largeCard.vue';

const roleChats = inject<Ref<any>>("roleChats");

onMounted(() => {
  initData();
});

const activeTab = ref<number>(1); // 1: 总体建议， 2: 新课标&核心素养， 3: 教学设计建议
const adviceData18 = ref<any>([]);
const adviceData24 = ref<any>([]);
const adviceData32 = ref<any>([]);

const initData = () => {
  if (roleChats?.value) {
    const roleChat3 = roleChats?.value[3] as any[];
    if (!roleChat3) return;
    const data18 = roleChat3.find((item) => item.questionType === 18);
    if (data18 && data18.answer) {
      const data18Answer = JSON.parse(data18.answer);
      adviceData18.value = data18Answer["建议"];
    }
    const data24 = roleChat3.find((item) => item.questionType === 24);
    if (data24 && data24.answer) {
      const data24Answer = JSON.parse(data24.answer);
      adviceData24.value = data24Answer;
    }
    const data32 = roleChat3.find((item) => item.questionType === 32);
    if (data32 && data32.answer) {
      const data32Answer = JSON.parse(data32.answer);
      adviceData32.value = data32Answer["建议"];
    }
  }
}

const handleTabClick = (index: number) => {
  activeTab.value = index;
};
</script>

<style lang="scss" scoped>
.teach-advice {
  width: 100%;
  height: 100%;
}
.tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 8px;
  background: #F8F8F8;
  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 12px 0;
    cursor: pointer;
    background-color: #fff;
    &:hover {
      background-color: #E6F6FF;
      color: #007AFF;
      .icon {
        color: #007AFF;
      };
    }

    .icon {
      font-size: 20px;
      color: #595959;
      margin-bottom: 8px;
    }
  }
  .active {
    background-color: #E6F6FF;
    color: #007AFF;
    .icon {
      color: #007AFF;
    };
  }
}
.content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}
.card-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 100%;
  gap: 20px;
}
.large-card-box {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  row-gap: 20px;
}
</style>