<template>
  <div class="large-card">
    <div class="title">
      <img src="@/assets/images/teach_advice.png" alt="" width="28" height="28">
      {{ props.title }}
    </div>
    <div class="content">
      <div class="left" @click="openContentModal(1)">
        <h3>优化方案</h3>
        <p class="ellipsis">{{ props.plan }}</p>
      </div>
      <div class="right" @click="openContentModal(2)">
        <h3>优化理由</h3>
        <p class="ellipsis">{{ props.reason }}</p>
      </div>
    </div>
  </div>

  <a-modal v-model:visible="contentModal" :footer="null" class="content-modal">
    <h3>{{ showType === 1 ? "优化方案" : "优化理由" }}</h3>
    <p>{{ showType === 1 ? props.plan : props.reason }}</p>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";

const props = defineProps<{
  title: string;
  plan: string;
  reason: string;
}>();

const contentModal = ref<boolean>(false);
const showType = ref<number>(1); // 1: 优化方案， 2: 优化理由

const openContentModal = (type: number) => {
  contentModal.value = true;
  showType.value = type;
}
</script>

<style scoped>
.large-card {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #F0F0F0;
  border-left: 3px solid #007AFF;
  .title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    height: 60px;
    padding-left: 20px;
    background-color: #FAFAFA;
    img {
      margin-right: 8px;
    }
  }
  .content {
    display: flex;
    height: 166px;
    .left, .right {
      width: 50%;
      padding: 20px 24px;
      cursor: pointer;
      h3 {
        font-size: 14px;
        text-align: center;
        margin-bottom: 16px;
      }
      p {
        line-height: 22px;
        margin-bottom: 0;
      }
    }
    .left {
      border-right: 1px dashed #E5E5E5;
    }
  }
}
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}
</style>

<style>
.content-modal {
  width: 480px !important;
  .ant-modal-body {
    padding: 32px;
  }
  h3 {
    font-size: 16px;
    text-align: center;
    margin-bottom: 20px;
  }
  p {
    line-height: 22px;
    margin-bottom: 0;
  }
}
</style>