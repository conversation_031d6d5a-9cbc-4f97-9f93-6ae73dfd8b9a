import axios from "axios";
import { message } from "ant-design-vue";
import { getUrlQuery, on2Login, on2NoPermission } from "./../common/path";
import { common } from "./../common";
import { devConfig } from "./../config";
export const Authorization = "Authorization";
export const base_url = "/base-api";
export const sd_url = "/sd-api";
export const mfile_url = "/sd-api/uploadFile";
const { getTokenFromCookie } = common;
const baseURL = process.env.NODE_ENV === "production" ? "" : devConfig.baseURL;
var service: any = null

service = new (window as any).ysCommonToolLib.AxiosWrapper(axios, {
  baseUrl: baseURL,
})

service.addRequest(
  (config) => {
    if (process.env.NODE_ENV === "production") {
      // config = (window as any).ysCommonToolLib.encryption(config);
      (config.headers as any).ysnettype = (
        window as any
      ).ysCommonToolLib._isInnerIP()
        ? 0
        : 1;
    }
    const cookieToken = getTokenFromCookie();
    if (cookieToken) {
      (config.headers as any)[Authorization] = cookieToken;
    }
    if (getUrlQuery("bureauId")) {
      (config.headers as any)["bureauId"] = getUrlQuery("bureauId");
    }
    if (getUrlQuery("jd")) {
      (config.headers as any)["jd"] = getUrlQuery("jd");
    }
    if (!(config.headers as any)["tenantId"]) {
      let tempTenant: any = localStorage.getItem("tempTenant") || "{}";
      tempTenant = JSON.parse(tempTenant);
      (config.headers as any)["tenantId"] = tempTenant.id || "0";
    }
    return config;
  },
  (error) => {
    console.log("error", error);
  }
);

service.addResponse(
  (response) => {
    const { data } = response;
    if (data.code !== 0) {
      if (!(response.config.headers as any).hideErrorTip) {
        errorHandler(data.code, data.msg);
      }
    }
    return response;
  },

  (error) => {
    if (error.config.headers.hideErrorTip) return;
    const code = error.response?.status;
    let msg = error.response.data?.message || error.response.data?.msg;
    if (msg === "GENERAL") {
      msg = "服务启动中...";
    }
    errorHandler(code, msg);
  }
);




function errorHandler(code: number, msg: string) {
  switch (code) {
    case 401:
      // 未登录
      if (process.env.NODE_ENV === "production") {
        on2Login();
      }
      break;
    case 403:
      // 没权限
      if (process.env.NODE_ENV === "production") {
        on2NoPermission();
      }
    case 404:
      // NOT FOUND
      msg && message.error(msg);
      break;
    case 500:
      // 业务接口异常
      msg && message.error(msg);
      break;
    default:
      console.log("NOT FOUND");
      break;
  }
}
const instance = service.getInstance()
export { instance };

